import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:watabot_mobile_app/core/theme/components/colors.component.dart';

class ConnectDeviceModal extends StatelessWidget {
  const ConnectDeviceModal({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: EdgeInsets.all(12.r),
      decoration: BoxDecoration(
        color: AppColor.cardBackground,
        borderRadius: BorderRadius.all(Radius.circular(40.r)),
      ),
      child: Padding(
        padding: EdgeInsets.all(20.r),
        child: Column(
          spacing: 5.h,
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Handle bar
            Center(
              child: Container(
                width: 40.w,
                height: 4.h,
                decoration: BoxDecoration(
                  color: AppColor.textSecondary.withValues(alpha: 0.3),
                  borderRadius: BorderRadius.circular(2.r),
                ),
              ),
            ),

            // Header
            Padding(
              padding: EdgeInsets.symmetric(vertical: 5.r),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    'Connect Device',
                    style: Theme.of(context).textTheme.headlineMedium,
                  ),
                  IconButton(
                    icon: Icon(Icons.close),
                    onPressed: () => Navigator.pop(context),
                  ),
                ],
              ),
            ),

            // Options
            ListTile(
              leading: Icon(Icons.add, color: AppColor.primaryColor),
              title: Text(
                'Connect with serial ID',
                style: Theme.of(context).textTheme.bodyLarge,
              ),
              onTap: () {
                // Handle serial ID connection
                Navigator.pop(context);
              },
            ),
            ListTile(
              leading: Icon(Icons.qr_code, color: AppColor.primaryColor),
              title: Text(
                'Connect with QR Code',
                style: Theme.of(context).textTheme.bodyLarge,
              ),
              onTap: () {
                // Handle QR code connection
                Navigator.pop(context);
              },
            ),
          ],
        ),
      ),
    );
  }
}
