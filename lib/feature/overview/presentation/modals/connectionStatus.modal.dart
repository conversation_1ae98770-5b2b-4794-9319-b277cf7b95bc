import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:watabot_mobile_app/core/theme/components/colors.component.dart';

enum ConnectionPhase { connecting, connected, deviceSetup }

class ConnectionStatusModal extends StatefulWidget {
  const ConnectionStatusModal({super.key});

  @override
  State<ConnectionStatusModal> createState() => _ConnectionStatusModalState();
}

class _ConnectionStatusModalState extends State<ConnectionStatusModal> {
  ConnectionPhase currentPhase = ConnectionPhase.connecting;
  final TextEditingController deviceNameController = TextEditingController(
    text: 'Device 1',
  );
  String selectedManufacturer = 'Select';
  String selectedBrand = 'Select';
  String selectedVolume = '• L';

  @override
  void initState() {
    super.initState();
    // Simulate connection process
    Timer(Duration(seconds: 3), () {
      if (mounted) {
        setState(() {
          currentPhase = ConnectionPhase.connected;
        });
        // After showing connected state for 2 seconds, move to device setup
        Timer(Duration(seconds: 2), () {
          if (mounted) {
            setState(() {
              currentPhase = ConnectionPhase.deviceSetup;
            });
          }
        });
      }
    });
  }

  @override
  void dispose() {
    deviceNameController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedContainer(
      duration: Duration(milliseconds: 300),
      decoration: BoxDecoration(
        color: AppColor.cardBackground,
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(20.r),
          topRight: Radius.circular(20.r),
        ),
      ),
      child: Padding(
        padding: EdgeInsets.all(24.r),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Handle bar
            Center(
              child: Container(
                width: 40.w,
                height: 4.h,
                decoration: BoxDecoration(
                  color: AppColor.textSecondary.withValues(alpha: 0.3),
                  borderRadius: BorderRadius.circular(2.r),
                ),
              ),
            ),
            SizedBox(height: 20.h),

            // Content based on current phase
            if (currentPhase == ConnectionPhase.deviceSetup) ...[
              _buildDeviceSetupContent(),
            ] else ...[
              _buildConnectionContent(),
            ],

            // Bottom padding for safe area
            SizedBox(height: MediaQuery.of(context).padding.bottom + 20.h),
          ],
        ),
      ),
    );
  }

  Widget _buildConnectionContent() {
    bool isConnected = currentPhase == ConnectionPhase.connected;
    String statusConnectedMessage = 'Device has been successfully connected!';
    String statusConnectingMessage =
        'Kindly wait as we listen for an update from the device. You can also trigger this update manually by using the magnet on its side.';

    return Column(
      children: [
        // Header with status and close button
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Row(
              children: [
                Container(
                  width: 12.w,
                  height: 12.h,
                  decoration: BoxDecoration(
                    color: isConnected ? Colors.green : Colors.orange,
                    shape: BoxShape.circle,
                  ),
                ),
                SizedBox(width: 8.w),
                Text(
                  isConnected ? 'Device Connected' : 'Connecting Device',
                  style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                    fontSize: 18.sp,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
            IconButton(
              icon: Icon(Icons.close, size: 24.sp),
              onPressed: () => Navigator.pop(context),
            ),
          ],
        ),
        SizedBox(height: 24.h),

        // Device info row
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'DF555',
                  style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                    fontSize: 16.sp,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                Text(
                  'Type',
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: AppColor.textSecondary.withValues(alpha: 0.7),
                    fontSize: 12.sp,
                  ),
                ),
              ],
            ),
            Column(
              crossAxisAlignment: CrossAxisAlignment.end,
              children: [
                Text(
                  'WL-23KJSD',
                  style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                    fontSize: 16.sp,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                Text(
                  'Serial ID',
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: AppColor.textSecondary.withValues(alpha: 0.7),
                    fontSize: 12.sp,
                  ),
                ),
              ],
            ),
          ],
        ),
        SizedBox(height: 24.h),

        // Sensor readings row
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  '10cm',
                  style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                    fontSize: 16.sp,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                Text(
                  'Sensor Level Reading',
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: AppColor.textSecondary.withValues(alpha: 0.7),
                    fontSize: 12.sp,
                  ),
                ),
              ],
            ),
            Column(
              crossAxisAlignment: CrossAxisAlignment.end,
              children: [
                Text(
                  '5mins ago',
                  style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                    fontSize: 16.sp,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                Text(
                  'Last Updated',
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: AppColor.textSecondary.withValues(alpha: 0.7),
                    fontSize: 12.sp,
                  ),
                ),
              ],
            ),
          ],
        ),
        SizedBox(height: 40.h),

        // Device illustration
        SizedBox(
          height: 180.h,
          child: Image.asset(
            'assets/images/device.png',
            height: 180.h,
            fit: BoxFit.contain,
            errorBuilder: (context, error, stackTrace) {
              return Container(
                height: 180.h,
                width: 120.w,
                decoration: BoxDecoration(
                  color: Color(0xFF5BA3B8),
                  borderRadius: BorderRadius.circular(12.r),
                ),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Container(
                      width: 80.w,
                      height: 100.h,
                      decoration: BoxDecoration(
                        color: Color(0xFF4A8FA3),
                        borderRadius: BorderRadius.circular(8.r),
                      ),
                    ),
                    SizedBox(height: 8.h),
                    Container(
                      width: 60.w,
                      height: 40.h,
                      decoration: BoxDecoration(
                        color: Color(0xFF3A7A8A),
                        borderRadius: BorderRadius.circular(20.r),
                      ),
                    ),
                  ],
                ),
              );
            },
          ),
        ),
        SizedBox(height: 40.h),

        // Status button
        Container(
          width: double.infinity,
          padding: EdgeInsets.symmetric(vertical: 16.h, horizontal: 24.w),
          decoration: BoxDecoration(
            color: AppColor.secondaryColor,
            borderRadius: BorderRadius.circular(25.r),
          ),
          child: Center(
            child: Text(
              isConnected ? 'CONNECTED' : 'AWAITING CONNECTION...',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                fontWeight: FontWeight.w600,
                fontSize: 14.sp,
                color: AppColor.textPrimary,
              ),
            ),
          ),
        ),
        SizedBox(height: 20.h),

        // Description text
        Padding(
          padding: EdgeInsets.symmetric(horizontal: 16.w),
          child: Text(
            isConnected ? statusConnectedMessage : statusConnectingMessage,
            textAlign: TextAlign.center,
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: AppColor.textSecondary.withValues(alpha: 0.8),
              fontSize: 13.sp,
              height: 1.4,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildDeviceSetupContent() {
    return Column(
      children: [
        // Header with status and close button
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Row(
              children: [
                Container(
                  width: 12.w,
                  height: 12.h,
                  decoration: BoxDecoration(
                    color: Colors.green,
                    shape: BoxShape.circle,
                  ),
                ),
                SizedBox(width: 8.w),
                Text(
                  'Device Connected',
                  style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                    fontSize: 18.sp,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
            IconButton(
              icon: Icon(Icons.close, size: 24.sp),
              onPressed: () => Navigator.pop(context),
            ),
          ],
        ),
        SizedBox(height: 24.h),

        // Device info row
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'DF555',
                  style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                    fontSize: 16.sp,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                Text(
                  'Type',
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: AppColor.textSecondary.withValues(alpha: 0.7),
                    fontSize: 12.sp,
                  ),
                ),
              ],
            ),
            Column(
              crossAxisAlignment: CrossAxisAlignment.end,
              children: [
                Text(
                  'WL-23KJSD',
                  style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                    fontSize: 16.sp,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                Text(
                  'Serial ID',
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: AppColor.textSecondary.withValues(alpha: 0.7),
                    fontSize: 12.sp,
                  ),
                ),
              ],
            ),
          ],
        ),
        SizedBox(height: 24.h),

        // Sensor readings row
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  '10cm',
                  style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                    fontSize: 16.sp,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                Text(
                  'Sensor Level Reading',
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: AppColor.textSecondary.withValues(alpha: 0.7),
                    fontSize: 12.sp,
                  ),
                ),
              ],
            ),
            Column(
              crossAxisAlignment: CrossAxisAlignment.end,
              children: [
                Text(
                  '5mins ago',
                  style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                    fontSize: 16.sp,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                Text(
                  'Last Updated',
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: AppColor.textSecondary.withValues(alpha: 0.7),
                    fontSize: 12.sp,
                  ),
                ),
              ],
            ),
          ],
        ),
        SizedBox(height: 40.h),

        // Device illustration
        SizedBox(
          height: 180.h,
          child: Image.asset(
            'assets/images/device.png',
            height: 180.h,
            fit: BoxFit.contain,
            errorBuilder: (context, error, stackTrace) {
              return Container(
                height: 180.h,
                width: 120.w,
                decoration: BoxDecoration(
                  color: Color(0xFF5BA3B8),
                  borderRadius: BorderRadius.circular(12.r),
                ),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Container(
                      width: 80.w,
                      height: 100.h,
                      decoration: BoxDecoration(
                        color: Color(0xFF4A8FA3),
                        borderRadius: BorderRadius.circular(8.r),
                      ),
                    ),
                    SizedBox(height: 8.h),
                    Container(
                      width: 60.w,
                      height: 40.h,
                      decoration: BoxDecoration(
                        color: Color(0xFF3A7A8A),
                        borderRadius: BorderRadius.circular(20.r),
                      ),
                    ),
                  ],
                ),
              );
            },
          ),
        ),
        SizedBox(height: 40.h),

        // Device Name Section
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text(
              deviceNameController.text,
              style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                fontSize: 24.sp,
                fontWeight: FontWeight.bold,
              ),
            ),
            SizedBox(width: 8.w),
            GestureDetector(
              onTap: () {
                // Show edit dialog or inline editing
                _showEditDeviceNameDialog();
              },
              child: Icon(
                Icons.edit,
                size: 20.sp,
                color: AppColor.textSecondary,
              ),
            ),
          ],
        ),
        Text(
          'Device Name',
          style: Theme.of(context).textTheme.bodySmall?.copyWith(
            color: AppColor.textSecondary.withValues(alpha: 0.7),
            fontSize: 12.sp,
          ),
        ),
        SizedBox(height: 32.h),

        // Tank Details Section
        Align(
          alignment: Alignment.centerLeft,
          child: Text(
            'Tank Details',
            style: Theme.of(context).textTheme.headlineMedium?.copyWith(
              fontSize: 18.sp,
              fontWeight: FontWeight.w600,
            ),
          ),
        ),
        SizedBox(height: 16.h),

        // Manufacturer Selection
        _buildSelectionRow(
          'Select',
          'Manufacturer',
          selectedManufacturer,
          () => _showManufacturerPicker(),
        ),
        SizedBox(height: 16.h),

        // Brand Selection
        _buildSelectionRow(
          'Select',
          'Brand',
          selectedBrand,
          () => _showBrandPicker(),
        ),
        SizedBox(height: 32.h),

        // Add New Device Button
        Container(
          width: double.infinity,
          child: ElevatedButton.icon(
            onPressed: () {
              // Handle add device
              Navigator.pop(context);
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColor.textPrimary,
              foregroundColor: AppColor.cardBackground,
              padding: EdgeInsets.symmetric(vertical: 16.h),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12.r),
              ),
            ),
            icon: Icon(Icons.add, size: 20.sp),
            label: Text(
              'Add New Device',
              style: TextStyle(fontSize: 16.sp, fontWeight: FontWeight.w600),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildSelectionRow(
    String defaultText,
    String label,
    String selectedValue,
    VoidCallback onTap,
  ) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 12.h),
        decoration: BoxDecoration(
          border: Border.all(
            color: AppColor.textSecondary.withValues(alpha: 0.3),
          ),
          borderRadius: BorderRadius.circular(8.r),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  selectedValue,
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    fontSize: 16.sp,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                Text(
                  label,
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: AppColor.textSecondary.withValues(alpha: 0.7),
                    fontSize: 12.sp,
                  ),
                ),
              ],
            ),
            if (label == 'Brand') ...[
              Row(
                children: [
                  Text(
                    selectedVolume,
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      fontSize: 16.sp,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  SizedBox(width: 8.w),
                  Text(
                    'Volume',
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: AppColor.textSecondary.withValues(alpha: 0.7),
                      fontSize: 12.sp,
                    ),
                  ),
                ],
              ),
            ] else ...[
              Container(
                width: 24.w,
                height: 24.h,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  border: Border.all(
                    color: AppColor.textSecondary.withValues(alpha: 0.3),
                    width: 2.w,
                  ),
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  void _showEditDeviceNameDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Edit Device Name'),
        content: TextField(
          controller: deviceNameController,
          decoration: InputDecoration(hintText: 'Enter device name'),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              setState(() {});
              Navigator.pop(context);
            },
            child: Text('Save'),
          ),
        ],
      ),
    );
  }

  void _showManufacturerPicker() {
    // Implement manufacturer picker
    showModalBottomSheet(
      context: context,
      builder: (context) => Container(
        height: 200.h,
        child: Center(child: Text('Manufacturer Picker - To be implemented')),
      ),
    );
  }

  void _showBrandPicker() {
    // Implement brand picker
    showModalBottomSheet(
      context: context,
      builder: (context) => Container(
        height: 200.h,
        child: Center(child: Text('Brand Picker - To be implemented')),
      ),
    );
  }
}
