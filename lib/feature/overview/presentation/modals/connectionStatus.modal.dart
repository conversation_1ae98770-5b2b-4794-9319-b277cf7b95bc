import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:watabot_mobile_app/core/theme/components/colors.component.dart';

class ConnectionStatusModal extends StatelessWidget {
  const ConnectionStatusModal({super.key});

  @override
  Widget build(BuildContext context) {
    bool isConnected = false;
    String statusConnectedMessage = 'Device has been successfully connected!';
    String statusConnectingMessage =
        'Kindly wait as we listen for an update from the device. You can also trigger this update manually by using the magnet on its side.';

    return Container(
      decoration: BoxDecoration(
        color: AppColor.cardBackground,
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(20.r),
          topRight: Radius.circular(20.r),
        ),
      ),
      child: Padding(
        padding: EdgeInsets.all(24.r),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Handle bar
            Center(
              child: Container(
                width: 40.w,
                height: 4.h,
                decoration: BoxDecoration(
                  color: AppColor.textSecondary.withValues(alpha: 0.3),
                  borderRadius: BorderRadius.circular(2.r),
                ),
              ),
            ),
            SizedBox(height: 20.h),

            // Header with status and close button
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Row(
                  children: [
                    Container(
                      width: 12.w,
                      height: 12.h,
                      decoration: BoxDecoration(
                        color: isConnected
                            ? AppColor.success
                            : AppColor.loading,
                        shape: BoxShape.circle,
                      ),
                    ),
                    SizedBox(width: 8.w),
                    Text(
                      isConnected ? 'Connected' : 'Connecting Device',
                      style: Theme.of(context).textTheme.headlineMedium
                          ?.copyWith(
                            fontSize: 18.sp,
                            fontWeight: FontWeight.w600,
                          ),
                    ),
                  ],
                ),
                IconButton(
                  icon: Icon(Icons.close, size: 24.sp),
                  onPressed: () => Navigator.pop(context),
                ),
              ],
            ),
            SizedBox(height: 24.h),

            // Device info row
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'DF555',
                      style: Theme.of(context).textTheme.headlineMedium
                          ?.copyWith(
                            fontSize: 16.sp,
                            fontWeight: FontWeight.bold,
                          ),
                    ),
                    Text(
                      'Type',
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: AppColor.textSecondary.withValues(alpha: 0.7),
                        fontSize: 12.sp,
                      ),
                    ),
                  ],
                ),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.end,
                  children: [
                    Text(
                      'WL-23KJSD',
                      style: Theme.of(context).textTheme.headlineMedium
                          ?.copyWith(
                            fontSize: 16.sp,
                            fontWeight: FontWeight.bold,
                          ),
                    ),
                    Text(
                      'Serial ID',
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: AppColor.textSecondary.withValues(alpha: 0.7),
                        fontSize: 12.sp,
                      ),
                    ),
                  ],
                ),
              ],
            ),
            SizedBox(height: 24.h),

            // Sensor readings row
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      '10cm',
                      style: Theme.of(context).textTheme.headlineMedium
                          ?.copyWith(
                            fontSize: 16.sp,
                            fontWeight: FontWeight.bold,
                          ),
                    ),
                    Text(
                      'Sensor Level Reading',
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: AppColor.textSecondary.withValues(alpha: 0.7),
                        fontSize: 12.sp,
                      ),
                    ),
                  ],
                ),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.end,
                  children: [
                    Text(
                      '5mins ago',
                      style: Theme.of(context).textTheme.headlineMedium
                          ?.copyWith(
                            fontSize: 16.sp,
                            fontWeight: FontWeight.bold,
                          ),
                    ),
                    Text(
                      'Last Updated',
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: AppColor.textSecondary.withValues(alpha: 0.7),
                        fontSize: 12.sp,
                      ),
                    ),
                  ],
                ),
              ],
            ),
            SizedBox(height: 40.h),

            // Device illustration
            SizedBox(
              height: 180.h,
              child: Image.asset(
                'assets/images/device.png', // You'll need to add this asset
                height: 180.h,
                fit: BoxFit.contain,
                errorBuilder: (context, error, stackTrace) {
                  // Fallback if image doesn't exist
                  return Container(
                    height: 180.h,
                    width: 120.w,
                    decoration: BoxDecoration(
                      color: Color(0xFF5BA3B8),
                      borderRadius: BorderRadius.circular(12.r),
                    ),
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Container(
                          width: 80.w,
                          height: 100.h,
                          decoration: BoxDecoration(
                            color: Color(0xFF4A8FA3),
                            borderRadius: BorderRadius.circular(8.r),
                          ),
                        ),
                        SizedBox(height: 8.h),
                        Container(
                          width: 60.w,
                          height: 40.h,
                          decoration: BoxDecoration(
                            color: Color(0xFF3A7A8A),
                            borderRadius: BorderRadius.circular(20.r),
                          ),
                        ),
                      ],
                    ),
                  );
                },
              ),
            ),
            SizedBox(height: 40.h),

            // Status button
            Container(
              width: double.infinity,
              padding: EdgeInsets.symmetric(vertical: 16.h, horizontal: 24.w),
              decoration: BoxDecoration(
                color: isConnected
                    ? AppColor.secondaryColor
                    : AppColor.loading_secondary,
                borderRadius: BorderRadius.circular(25.r),
              ),
              child: Center(
                child: Text(
                  isConnected ? 'CONNECTED' : 'AWAITING CONNECTION...',
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                    fontSize: 14.sp,
                    color: AppColor.textPrimary,
                  ),
                ),
              ),
            ),
            SizedBox(height: 20.h),

            // Description text
            Padding(
              padding: EdgeInsets.symmetric(horizontal: 16.w),
              child: Text(
                isConnected ? statusConnectedMessage : statusConnectingMessage,
                textAlign: TextAlign.center,
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: AppColor.textSecondary.withValues(alpha: 0.8),
                  fontSize: 13.sp,
                  height: 1.4,
                ),
              ),
            ),

            // Bottom padding for safe area
            SizedBox(height: MediaQuery.of(context).padding.bottom + 20.h),
          ],
        ),
      ),
    );
  }
}
