import 'dart:ui';

import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:watabot_mobile_app/core/theme/components/colors.component.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:watabot_mobile_app/feature/overview/presentation/modals/connectionStatus.modal.dart';

@RoutePage()
class OverviewScreen extends StatelessWidget {
  const OverviewScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SafeArea(
        child: Container(
          color: AppColor.background,
          child: SafeArea(
            child: Stack(
              children: [
                // Main scrollable content
                Padding(
                  padding: EdgeInsets.only(top: 80.h), // Space for app bar
                  child: Container(
                    margin: EdgeInsets.symmetric(horizontal: 10.r),
                    decoration: BoxDecoration(
                      color: AppColor.secondaryBackground,
                      borderRadius: BorderRadius.all(Radius.circular(40.r)),
                    ),
                    child: Padding(
                      padding: EdgeInsets.all(24.r),
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          // Greeting text
                          Text(
                            'Good Day',
                            style: Theme.of(context).textTheme.headlineLarge,
                          ),

                          Text(
                            'Ronald',
                            style: Theme.of(context).textTheme.bodyLarge,
                          ),

                          SizedBox(height: 50.h),

                          // Device illustration
                          Image.asset(
                            'assets/images/emptyState_device.png',
                            height: 225.h,
                          ),

                          SizedBox(height: 50.h),

                          // No Device Installed text
                          Text(
                            'No Device Installed',
                            style: Theme.of(context).textTheme.headlineMedium,
                          ),

                          SizedBox(height: 16.h),

                          // Description text
                          Text(
                            'You don\'t have any device installed.\nQuickly and simply connect one to start measuring.',
                            textAlign: TextAlign.center,
                            style: Theme.of(context).textTheme.bodySmall,
                          ),

                          Spacer(),

                          // Connect button
                          InkWell(
                            onTap: () => showModalBottomSheet(
                              context: context,
                              isScrollControlled: true,
                              backgroundColor: Colors.transparent,
                              builder: (context) => ConnectionStatusModal(),
                            ),
                            child: ClipRRect(
                              borderRadius: BorderRadius.circular(50.r),
                              child: Container(
                                padding: EdgeInsets.all(12.r),
                                decoration: BoxDecoration(
                                  color: AppColor.bottomNavColor,
                                  borderRadius: BorderRadius.all(
                                    Radius.circular(50.r),
                                  ),
                                ),
                                child: BackdropFilter(
                                  filter: ImageFilter.blur(
                                    sigmaX: 10,
                                    sigmaY: 10,
                                  ),
                                  child: OutlinedButton.icon(
                                    onPressed:
                                        null, // Disabled since InkWell handles the tap
                                    icon: Icon(Icons.add),
                                    label: Text('CONNECT'),
                                  ),
                                ),
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),

                // Transparent App Bar
                Container(
                  height: 80.h,
                  padding: EdgeInsets.symmetric(horizontal: 20.w),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      // Logo
                      Image.asset('assets/images/logo.png', height: 20.h),

                      // User Avatar
                      Container(
                        width: 40.w,
                        height: 40.h,
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(20.r),
                          border: Border.all(
                            color: AppColor.secondaryColor,
                            width: 4.w,
                          ),
                        ),
                        child: ClipRRect(
                          borderRadius: BorderRadius.circular(18.r),
                          child: CachedNetworkImage(
                            imageUrl:
                                'https://api.dicebear.com/9.x/notionists/jpg?seed=Felix',

                            fit: BoxFit.cover,
                            placeholder: (context, url) => Container(
                              color: AppColor.secondaryBackground,
                              child: Icon(Icons.person, size: 20.sp),
                            ),
                            errorWidget: (context, url, error) => Container(
                              color: AppColor.secondaryBackground,
                              child: Icon(Icons.person, size: 20.sp),
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
