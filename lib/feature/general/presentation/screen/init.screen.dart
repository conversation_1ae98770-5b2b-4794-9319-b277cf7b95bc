import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:watabot_mobile_app/core/route/app.router.gr.dart';

@RoutePage()
class InitScreen extends StatelessWidget {
  const InitScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.start,
        spacing: 12.h,
        children: [
          InkWell(
            child: Text('Onboarding Screen'),
            onTap: () => context.router.push(OnboardingRoute()),
          ),

          InkWell(
            child: Text('Register Screen'),
            onTap: () => context.router.push(RegisterRoute()),
          ),

          InkWell(
            child: Text('Login Screen'),
            onTap: () => context.router.push(LoginRoute()),
          ),

          InkWell(
            child: Text('Overview Screen'),
            onTap: () => context.router.push(OverviewRoute()),
          ),
        ],
      ),
    );
  }
}
