import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:watabot_mobile_app/core/route/app.router.dart';
import 'package:watabot_mobile_app/core/theme/app.theme.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  await SystemChrome.setPreferredOrientations([
    DeviceOrientation.portraitUp,
    DeviceOrientation.portraitDown,
  ]);

  await dotenv.load();

  runApp(Watabot());
}

class Watabot extends ConsumerWidget {
  Watabot({Key? key}) : super(key: key);

  final _appRouter = AppRouter();

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return ProviderScope(
      child: ScreenUtilInit(
        useInheritedMediaQuery: true,
        designSize: const Size(390, 844), // iPhone 14 Pro size (width x height)
        minTextAdapt: true, // automatically scale text
        ensureScreenSize: true,
        builder: (context, child) {
          return MaterialApp.router(
            theme: AppTheme.lightTheme(),
            routeInformationParser: _appRouter.defaultRouteParser(),
            routerDelegate: _appRouter.delegate(),
            debugShowCheckedModeBanner: false,
            builder: (context, widget) {
              return widget!;
            },
          );
        },
      ),
    );
  }
}
