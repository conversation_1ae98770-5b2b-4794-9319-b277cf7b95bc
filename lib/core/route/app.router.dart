import 'package:auto_route/auto_route.dart';
import 'package:watabot_mobile_app/core/route/app.router.gr.dart';

@AutoRouterConfig(replaceInRouteName: 'Screen|Page,Route')
class AppRouter extends RootStackRouter {
  @override
  List<AutoRoute> get routes => [
    AutoRoute(page: InitRoute.page, initial: true),

    //AUTHENTICATION
    AutoRoute(page: OnboardingRoute.page),
    AutoRoute(page: LoginRoute.page),
    AutoRoute(page: RegisterRoute.page),

    //OVERVIEW
    AutoRoute(page: OverviewRoute.page),
  ];
}
