import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:watabot_mobile_app/core/theme/components/colors.component.dart';
import 'package:watabot_mobile_app/core/theme/components/textStyles.component.dart';

class AppButtonStyles {
  static final ButtonStyle primary = ButtonStyle(
    backgroundColor: WidgetStateProperty.resolveWith((states) {
      if (states.contains(WidgetState.pressed)) {
        return AppColor.primaryColor.withValues(alpha: 0.8);
      }
      return AppColor.secondaryColor;
    }),
    foregroundColor: WidgetStateProperty.all(AppColor.primaryColor),
    textStyle: WidgetStateProperty.all(AppTextStyles.body()),
    shape: WidgetStateProperty.all(
      RoundedRectangleBorder(borderRadius: BorderRadius.circular(50.r)),
    ),
    side: WidgetStateProperty.all(
      BorderSide(color: AppColor.stroke, width: 3.w),
    ),
    padding: WidgetStateProperty.all(
      EdgeInsets.symmetric(horizontal: 20.w, vertical: 12.h),
    ),
  );

  static final ButtonStyle secondary = ButtonStyle(
    backgroundColor: WidgetStateProperty.all(AppColor.secondaryColor),
    foregroundColor: WidgetStateProperty.all(AppColor.cardBackground),
    textStyle: WidgetStateProperty.all(AppTextStyles.buttonLabel()),
    shape: WidgetStateProperty.all(
      RoundedRectangleBorder(borderRadius: BorderRadius.circular(12.r)),
    ),
    padding: WidgetStateProperty.all(
      EdgeInsets.symmetric(horizontal: 20.w, vertical: 12.h),
    ),
  );
}
