import 'package:flutter/material.dart';

class AppColor {
  // Primary & Secondary
  static const primaryColor = Color.fromARGB(255, 0, 0, 0); // vibrant blue
  static const secondaryColor = Color(0xFFD2FFDF); // teal accent

  // Backgrounds
  static const background = Color(0xFFD8ECFF); // light, soft background
  static const secondaryBackground = Color(
    0xFFF4FDFF,
  ); // slightly darker background
  static const cardBackground = Color(0xFFFFFFFF);
  static const bottomNavColor = Color(0xFFA1CED1);

  // Text
  static const textPrimary = Color(0xFF212121); // dark grey
  static const textSecondary = Color(0xFF4A4863); // grey

  // Status indicators
  static const waterHigh = Color(0xFFD32F2F); // red
  static const waterMedium = Color(0xFFFFA000); // amber
  static const waterLow = Color(0xFF43A047); // green

  // UI elements
  static const stroke = Color.fromARGB(255, 0, 0, 0); // borders/shadows
  static const iconColor = Color.fromARGB(255, 7, 16, 24); // icons primary
}
