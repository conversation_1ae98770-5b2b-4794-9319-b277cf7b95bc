import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:watabot_mobile_app/core/theme/components/colors.component.dart';
import 'package:watabot_mobile_app/core/theme/components/textStyles.component.dart';

class AppInputStyles {
  static InputDecorationTheme primaryTheme() => InputDecorationTheme(
    filled: true,
    fillColor: AppColor.cardBackground,
    contentPadding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 14.h),
    border: OutlineInputBorder(
      borderRadius: BorderRadius.circular(12.r),
      borderSide: BorderSide(color: AppColor.stroke),
    ),
    enabledBorder: OutlineInputBorder(
      borderRadius: BorderRadius.circular(12.r),
      borderSide: BorderSide(color: AppColor.stroke),
    ),
    focusedBorder: OutlineInputBorder(
      borderRadius: BorderRadius.circular(12.r),
      borderSide: BorderSide(color: AppColor.primaryColor, width: 1.5),
    ),
    labelStyle: AppTextStyles.body(),
    hintStyle: AppTextStyles.body().copyWith(color: AppColor.textSecondary),
  );
}
