import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:watabot_mobile_app/core/theme/components/colors.component.dart';
import 'package:google_fonts/google_fonts.dart';

class AppTextStyles {
  static TextStyle heading() => GoogleFonts.poppins(
    fontSize: 27.sp,
    fontWeight: FontWeight.w900,
    color: AppColor.textPrimary,
  );

  static TextStyle subHeading() => GoogleFonts.poppins(
    fontSize: 20.sp,
    fontWeight: FontWeight.w900,
    color: AppColor.textPrimary,
  );

  static TextStyle bodyLarge() => GoogleFonts.poppins(
    fontSize: 16.sp,
    fontWeight: FontWeight.w600,
    color: AppColor.textSecondary,
  );

  static TextStyle body() => GoogleFonts.poppins(
    fontSize: 16.sp,
    fontWeight: FontWeight.normal,
    color: AppColor.textSecondary,
  );

  static TextStyle bodySmall() => GoogleFonts.poppins(
    fontSize: 15.sp,
    fontWeight: FontWeight.normal,
    color: AppColor.textSecondary,
  );

  static TextStyle statusLabel() => GoogleFonts.poppins(
    fontSize: 14.sp,
    fontWeight: FontWeight.w500,
    color: AppColor.textSecondary,
  );

  static TextStyle buttonLabel() => GoogleFonts.roboto(
    fontSize: 11.sp,
    fontWeight: FontWeight.bold,
    color: AppColor.stroke,
  );

  static TextTheme baseTextTheme() => TextTheme(
    headlineLarge: heading(),
    headlineMedium: subHeading(),
    bodyLarge: bodyLarge(),
    bodyMedium: body(),
    bodySmall: bodySmall(),
    labelLarge: statusLabel(),
  );
}
