import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:watabot_mobile_app/core/theme/components/button.component.dart';
import 'package:watabot_mobile_app/core/theme/components/colors.component.dart';
import 'package:watabot_mobile_app/core/theme/components/inputStyle.component.dart';
import 'package:watabot_mobile_app/core/theme/components/textStyles.component.dart';
import 'package:google_fonts/google_fonts.dart';

class AppTheme {
  static ThemeData lightTheme() {
    final base = ThemeData.light();

    return base.copyWith(
      scaffoldBackgroundColor: AppColor.background,
      colorScheme: ColorScheme.light(
        primary: AppColor.primaryColor,
        secondary: AppColor.secondaryColor,
      ),
      textTheme: AppTextStyles.baseTextTheme(),
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: AppButtonStyles.primary,
      ),
      outlinedButtonTheme: OutlinedButtonThemeData(
        style: AppButtonStyles.primary,
      ),
      inputDecorationTheme: AppInputStyles.primaryTheme(),
      iconTheme: IconThemeData(color: AppColor.iconColor, size: 22.sp),
      appBarTheme: AppBarTheme(
        backgroundColor: AppColor.primaryColor,
        foregroundColor: AppColor.cardBackground,
        elevation: 2,
        centerTitle: true,
        iconTheme: IconThemeData(color: AppColor.cardBackground, size: 24.sp),
        titleTextStyle: GoogleFonts.poppins(
          fontSize: 27.sp,
          fontWeight: FontWeight.bold,
          color: AppColor.textPrimary,
        ),
      ),
    );
  }
}
