# Watabot

[![License](https://img.shields.io/badge/License-MIT-blue.svg)](LICENSE)

> The purpose of this README is to give developers insight into the Watabot app and how it works.

## Introduction
Watabot is a Flutter-based app that allows users to measure water level, monitor water consumption, and receive notifications. This app provides an easy and convenient way to track water usage and stay informed about water levels.

## Features
The key features of the Watabot app include:

1. Measuring water level: The app allows users to measure the current water level in a container or tank.

2. Measuring water consumption: Users can track their water consumption over a specified period, helping them become more aware of their usage habits.

3. Notifications: The app sends notifications to users when the water level reaches a certain threshold or when specific events occur.

## Built With
The Watabot app is built with the following technologies:

- Flutter: The app is developed using the Flutter framework, enabling cross-platform compatibility.

## Plugins
The major plugins used in the app include:

- Google Realtime Database: This plugin is utilized for real-time updates of the water level, ensuring accurate and up-to-date information.

- Notifications: The app integrates with the notification system of the operating system to deliver timely alerts and reminders.

## Authorship
This document is authored by <PERSON>.

## License
This project is licensed under the [MIT License](LICENSE). Please see the license file for more details.

## Organization
This project is developed by Tech Era.


